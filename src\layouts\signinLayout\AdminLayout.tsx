'use client';

import { useState } from 'react';

import Header from './Header';
import Sidebar from './Sidebar';

export default function Layout({ children }: { children: React.ReactNode }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar
        isOpenFromHeader={isSidebarOpen}
        setIsOpenFromHeader={setIsSidebarOpen}
      />
      <div className="flex flex-col flex-1">
        <div className="sticky top-0 z-10 bg-white">
          <Header onSidebarToggle={() => setIsSidebarOpen(prev => !prev)} />
        </div>
        <main className="flex-1 max-h-[calc(100vh-73px)] overflow-auto ml-6 pt-0 bg-[#F5F5F5] rounded-tl-2xl ">
          {children}
        </main>
      </div>
    </div>
  );
}
