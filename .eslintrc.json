{"extends": ["next/core-web-vitals", "airbnb", "airbnb/hooks", "prettier"], "plugins": ["simple-import-sort", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "ecmaFeatures": {"jsx": true}, "project": "./tsconfig.json"}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}, "node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}, "import/extensions": [".js", ".jsx", ".ts", ".tsx"], "import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}}, "rules": {"simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "prettier/prettier": "error", "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "import/prefer-default-export": "off", "react/jsx-filename-extension": [1, {"extensions": [".tsx", ".jsx"]}], "react/react-in-jsx-scope": "off", "react/function-component-definition": [2, {"namedComponents": "arrow-function", "unnamedComponents": "arrow-function"}], "@typescript-eslint/no-unused-vars": "error", "no-unused-vars": "off"}, "env": {"browser": true, "es2021": true, "node": true}}