import { heroui } from '@heroui/react';

/** @type {import('tailwindcss').Config} */
export default {
  content: ['./app/**/*.{js,ts,jsx,tsx}', './components/**/*.{js,ts,jsx,tsx}'],

  plugins: [
    heroui({
      themes: {
        light: {
          colors: {
            background: '#FFFFFF',
            foreground: '#11181C',
            primary: {
              foreground: '#FFFFFF',
              DEFAULT: '#707FF5',
            },
            subtitle: '#1C1C1C',
          },
        },
      },
    }),
  ],
};
